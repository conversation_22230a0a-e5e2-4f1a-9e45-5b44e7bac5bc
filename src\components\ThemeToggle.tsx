'use client';

import { useTheme } from '@/hooks/useTheme';
import { useState, useEffect } from 'react';

interface ThemeToggleProps {
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  showLabel?: boolean;
}

export default function ThemeToggle({ 
  className = '', 
  size = 'md',
  showLabel = false 
}: ThemeToggleProps) {
  const { theme, toggleTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  // 防止服务端渲染不匹配
  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return (
      <div className={`animate-pulse bg-gray-200 dark:bg-gray-700 rounded-lg ${getSizeClasses(size)} ${className}`} />
    );
  }

  const isDark = theme === 'dark';

  return (
    <button
      onClick={toggleTheme}
      className={`
        relative inline-flex items-center justify-center
        rounded-lg border border-border
        bg-card text-card-foreground
        hover:bg-muted hover:border-border-hover
        focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2
        transition-all duration-200 ease-in-out
        ${getSizeClasses(size)}
        ${className}
      `}
      aria-label={`切换到${isDark ? '浅色' : '深色'}模式`}
      title={`当前: ${isDark ? '深色' : '浅色'}模式，点击切换`}
    >
      <div className="relative">
        {/* 太阳图标 (浅色模式) */}
        <svg
          className={`
            absolute inset-0 transition-all duration-300 ease-in-out
            ${isDark ? 'opacity-0 rotate-90 scale-0' : 'opacity-100 rotate-0 scale-100'}
            ${getIconSize(size)}
          `}
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
          strokeWidth={2}
        >
          <circle cx="12" cy="12" r="5" />
          <path d="M12 1v2M12 21v2M4.22 4.22l1.42 1.42M18.36 18.36l1.42 1.42M1 12h2M21 12h2M4.22 19.78l1.42-1.42M18.36 5.64l1.42-1.42" />
        </svg>

        {/* 月亮图标 (深色模式) */}
        <svg
          className={`
            transition-all duration-300 ease-in-out
            ${isDark ? 'opacity-100 rotate-0 scale-100' : 'opacity-0 -rotate-90 scale-0'}
            ${getIconSize(size)}
          `}
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
          strokeWidth={2}
        >
          <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z" />
        </svg>
      </div>

      {/* 可选的文字标签 */}
      {showLabel && (
        <span className={`ml-2 text-sm font-medium ${size === 'sm' ? 'hidden sm:inline' : ''}`}>
          {isDark ? '深色' : '浅色'}
        </span>
      )}

      {/* 切换动画指示器 */}
      <div
        className={`
          absolute inset-0 rounded-lg border-2 border-accent
          transition-all duration-200 ease-in-out
          ${isDark ? 'opacity-0 scale-95' : 'opacity-0 scale-95'}
          pointer-events-none
        `}
        style={{
          animation: mounted ? 'none' : 'pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite'
        }}
      />
    </button>
  );
}

// 获取尺寸相关的CSS类
function getSizeClasses(size: 'sm' | 'md' | 'lg'): string {
  switch (size) {
    case 'sm':
      return 'h-8 w-8 p-1.5';
    case 'md':
      return 'h-10 w-10 p-2';
    case 'lg':
      return 'h-12 w-12 p-2.5';
    default:
      return 'h-10 w-10 p-2';
  }
}

// 获取图标尺寸
function getIconSize(size: 'sm' | 'md' | 'lg'): string {
  switch (size) {
    case 'sm':
      return 'h-4 w-4';
    case 'md':
      return 'h-5 w-5';
    case 'lg':
      return 'h-6 w-6';
    default:
      return 'h-5 w-5';
  }
}

// 带标签的主题切换组件变体
export function ThemeToggleWithLabel({ className = '', size = 'md' }: Omit<ThemeToggleProps, 'showLabel'>) {
  return <ThemeToggle className={className} size={size} showLabel={true} />;
}

// 紧凑型主题切换组件
export function CompactThemeToggle({ className = '' }: Pick<ThemeToggleProps, 'className'>) {
  return <ThemeToggle className={className} size="sm" />;
}
