'use client';

import { useState, useEffect } from 'react';
import Header from '@/components/Header';
import MasonryGrid from '@/components/MasonryGrid';
import { PinData } from '@/components/PinCard';
import { loadPinsFromJson } from '@/services/dataService';
import { loadAvatarConfig } from '@/services/avatarService';
import { useSortedPins } from '@/hooks/useSortedPins';
import { useDebounce } from '@/hooks/useDebounce';
import { useAvatarPreload } from '@/hooks/useAvatarPreload';
import { usePerformanceMonitor } from '@/hooks/usePerformanceMonitor';
import { SortType } from '@/utils/sortUtils';

export default function Home() {
  const [pins, setPins] = useState<PinData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [hasMore, setHasMore] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<SortType>('latest');
  const [filterBy, setFilterBy] = useState('all');
  const [sortLoading, setSortLoading] = useState(false);

  // 使用防抖优化搜索性能
  const debouncedSearchQuery = useDebounce(searchQuery, 300);

  // 使用优化后的排序Hook
  const { sortedPins } = useSortedPins(
    pins,
    debouncedSearchQuery,
    filterBy,
    sortBy
  );

  // 使用头像预加载Hook
  useAvatarPreload(pins);

  // 使用性能监控Hook
  const { getStats: getRequestStats } = usePerformanceMonitor();

  // 初始化加载数据
  useEffect(() => {
    const initializeData = async () => {
      try {
        setLoading(true);
        setError(null);

        // 并行加载精华消息数据和头像配置
        const [loadedPins] = await Promise.all([
          loadPinsFromJson(),
          loadAvatarConfig() // 预加载头像配置
        ]);

        setPins(loadedPins);

        // JSON文件是一次性加载，所以没有更多数据
        setHasMore(false);

      } catch (err) {
        console.error('数据加载失败:', err);
        setError(err instanceof Error ? err.message : '数据加载失败');
      } finally {
        setLoading(false);
      }
    };

    initializeData();
  }, []);

  // 搜索功能
  const handleSearch = (query: string) => {
    setSearchQuery(query);
  };

  // 排序功能
  const handleSortChange = (sort: string) => {
    setSortLoading(true);
    setSortBy(sort as SortType);
    // 排序完成后重置加载状态
    setTimeout(() => setSortLoading(false), 100);
  };

  // 筛选功能
  const handleFilterChange = (filter: string) => {
    setFilterBy(filter);
  };



  // 轻量级性能监控（仅开发环境且有问题时）
  useEffect(() => {
    if (process.env.NODE_ENV === 'development' && sortedPins.length > 100) {
      // 只在数据量大时才输出性能信息
      const requestStats = getRequestStats();
      if (requestStats && (requestStats.queueLength > 0 || requestStats.currentRequests > 0)) {
        console.log('📊 大数据集性能:', {
          数据量: sortedPins.length,
          请求状态: requestStats
        });
      }
    }
  }, [sortedPins.length, getRequestStats]); // 减少依赖，降低触发频率



  // 错误状态显示
  if (error) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="text-destructive text-lg mb-4">数据加载失败</div>
          <div className="text-muted-foreground mb-4">{error}</div>
          <button
            onClick={() => window.location.reload()}
            className="btn-primary px-4 py-2 rounded-lg transition-colors"
          >
            重新加载
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <Header
        onSearch={handleSearch}
        onSortChange={handleSortChange}
        onFilterChange={handleFilterChange}
        sortLoading={sortLoading}
      />

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {loading ? (
          <div className="flex justify-center items-center py-16">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-accent mx-auto mb-4"></div>
              <div className="text-muted-foreground">正在加载精华消息...</div>
            </div>
          </div>
        ) : (
          <MasonryGrid
            pins={sortedPins}
            hasMore={hasMore}
            loading={sortLoading}
            searchQuery={debouncedSearchQuery}
          />
        )}
      </main>
    </div>
  );
}
