'use client';

import { useTheme } from '@/hooks/useTheme';

export default function ThemeDebug() {
  try {
    const { theme } = useTheme();
    return (
      <div className="fixed bottom-4 right-4 bg-card border border-border rounded-lg p-2 text-xs">
        <div>主题: {theme}</div>
        <div>Context: 正常</div>
      </div>
    );
  } catch (error) {
    return (
      <div className="fixed bottom-4 right-4 bg-red-100 border border-red-300 rounded-lg p-2 text-xs text-red-800">
        <div>错误: {error instanceof Error ? error.message : '未知错误'}</div>
      </div>
    );
  }
}
