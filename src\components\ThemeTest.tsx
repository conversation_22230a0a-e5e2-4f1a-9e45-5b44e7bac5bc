'use client';

import { useTheme } from '@/hooks/useTheme';
import ThemeToggle from './ThemeToggle';
import PinCard from './PinCard';
import { PinData } from './PinCard';

// 测试数据
const testPin: PinData = {
  id: 'test-1',
  content: '这是一条测试精华消息，用于验证明暗模式的显示效果。包含中文和English混合内容。',
  contentType: 'text',
  contentElements: [],
  timestamp: new Date().toISOString(),
  setter: {
    name: '测试设置者',
    avatar: ''
  },
  sender: {
    name: '测试发送者',
    avatar: ''
  },
  groupName: '测试群组'
};

export default function ThemeTest() {
  const { theme } = useTheme();

  return (
    <div className="min-h-screen bg-background p-8">
      <div className="max-w-4xl mx-auto space-y-8">
        {/* 主题信息 */}
        <div className="bg-card border border-border rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h1 className="text-2xl font-bold text-foreground">主题测试页面</h1>
            <ThemeToggle size="md" showLabel />
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-muted-foreground">当前主题:</span>
              <span className="ml-2 text-accent font-medium">
                {theme === 'dark' ? '深色模式' : '浅色模式'}
              </span>
            </div>
            <div>
              <span className="text-muted-foreground">主题类:</span>
              <span className="ml-2 text-card-foreground font-mono">
                {document.documentElement.className}
              </span>
            </div>
          </div>
        </div>

        {/* 颜色变量展示 */}
        <div className="bg-card border border-border rounded-lg p-6">
          <h2 className="text-xl font-semibold text-foreground mb-4">颜色变量测试</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <div className="w-full h-12 bg-background border border-border rounded"></div>
              <p className="text-xs text-muted-foreground">background</p>
            </div>
            <div className="space-y-2">
              <div className="w-full h-12 bg-card border border-border rounded"></div>
              <p className="text-xs text-muted-foreground">card</p>
            </div>
            <div className="space-y-2">
              <div className="w-full h-12 bg-muted border border-border rounded"></div>
              <p className="text-xs text-muted-foreground">muted</p>
            </div>
            <div className="space-y-2">
              <div className="w-full h-12 bg-accent border border-border rounded"></div>
              <p className="text-xs text-muted-foreground">accent</p>
            </div>
          </div>
        </div>

        {/* 文字颜色测试 */}
        <div className="bg-card border border-border rounded-lg p-6">
          <h2 className="text-xl font-semibold text-foreground mb-4">文字颜色测试</h2>
          <div className="space-y-3">
            <p className="text-foreground">主要文字 (foreground)</p>
            <p className="text-card-foreground">卡片文字 (card-foreground)</p>
            <p className="text-muted-foreground">次要文字 (muted-foreground)</p>
            <p className="text-accent">强调文字 (accent)</p>
            <p className="text-destructive">错误文字 (destructive)</p>
          </div>
        </div>

        {/* 组件测试 */}
        <div className="bg-card border border-border rounded-lg p-6">
          <h2 className="text-xl font-semibold text-foreground mb-4">组件测试</h2>
          
          {/* 按钮测试 */}
          <div className="mb-6">
            <h3 className="text-lg font-medium text-card-foreground mb-3">按钮样式</h3>
            <div className="flex flex-wrap gap-3">
              <button className="btn-primary px-4 py-2 rounded-lg">主要按钮</button>
              <button className="btn-secondary px-4 py-2 rounded-lg">次要按钮</button>
              <ThemeToggle size="sm" />
              <ThemeToggle size="md" />
              <ThemeToggle size="lg" />
            </div>
          </div>

          {/* 输入框测试 */}
          <div className="mb-6">
            <h3 className="text-lg font-medium text-card-foreground mb-3">输入框样式</h3>
            <input
              type="text"
              placeholder="测试输入框..."
              className="input-themed w-full max-w-md px-3 py-2 border rounded-lg"
            />
          </div>

          {/* PinCard测试 */}
          <div>
            <h3 className="text-lg font-medium text-card-foreground mb-3">精华卡片</h3>
            <div className="max-w-md">
              <PinCard pin={testPin} searchQuery="测试" />
            </div>
          </div>
        </div>

        {/* 可访问性信息 */}
        <div className="bg-card border border-border rounded-lg p-6">
          <h2 className="text-xl font-semibold text-foreground mb-4">可访问性检查</h2>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-muted-foreground">主要文字对比度:</span>
              <span className="text-card-foreground">
                {theme === 'dark' ? '高对比度 (WCAG AAA)' : '高对比度 (WCAG AAA)'}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">次要文字对比度:</span>
              <span className="text-card-foreground">
                {theme === 'dark' ? '中等对比度 (WCAG AA)' : '中等对比度 (WCAG AA)'}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">焦点指示器:</span>
              <span className="text-card-foreground">蓝色环形，2px宽度</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
