@import "tailwindcss";

/* 浅色主题变量 */
:root {
  --background: #ffffff;
  --foreground: #171717;
  --card-background: #ffffff;
  --card-foreground: #171717;
  --border: #e5e7eb;
  --border-hover: #d1d5db;
  --muted: #f9fafb;
  --muted-foreground: #6b7280;
  --accent: #3b82f6;
  --accent-foreground: #ffffff;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --ring: #3b82f6;
  --input-background: #ffffff;
  --input-border: #d1d5db;
  --header-background: #ffffff;
  --header-border: #e5e7eb;
  --scrollbar-track: #f1f1f1;
  --scrollbar-thumb: #c1c1c1;
  --scrollbar-thumb-hover: #a8a8a8;
}

/* 深色主题变量 */
.dark {
  --background: #0f172a;
  --foreground: #f1f5f9;
  --card-background: #1e293b;
  --card-foreground: #f1f5f9;
  --border: #334155;
  --border-hover: #475569;
  --muted: #1e293b;
  --muted-foreground: #94a3b8;
  --accent: #3b82f6;
  --accent-foreground: #ffffff;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --ring: #3b82f6;
  --input-background: #1e293b;
  --input-border: #475569;
  --header-background: #1e293b;
  --header-border: #334155;
  --scrollbar-track: #334155;
  --scrollbar-thumb: #64748b;
  --scrollbar-thumb-hover: #94a3b8;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card-background: var(--card-background);
  --color-card-foreground: var(--card-foreground);
  --color-border: var(--border);
  --color-border-hover: var(--border-hover);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-ring: var(--ring);
  --color-input-background: var(--input-background);
  --color-input-border: var(--input-border);
  --color-header-background: var(--header-background);
  --color-header-border: var(--header-border);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-geist-sans), Arial, Helvetica, sans-serif;
  transition: background-color 0.2s ease-in-out, color 0.2s ease-in-out;
}

/* 自定义滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--scrollbar-track);
}

::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--scrollbar-thumb-hover);
}

/* 平滑滚动 */
html {
  scroll-behavior: smooth;
}

/* 图片加载动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

/* 卡片悬停效果增强 */
.card-hover {
  transition: all 0.2s ease-in-out;
}

.card-hover:hover {
  transform: translateY(-2px);
}

/* 浅色主题悬停阴影 */
.light .card-hover:hover {
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* 深色主题悬停阴影 */
.dark .card-hover:hover {
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
}

/* 响应式文字大小 */
@media (max-width: 640px) {
  body {
    font-size: 14px;
  }
}

/* 焦点样式优化 */
.focus-ring:focus {
  outline: none;
  ring: 2px;
  ring-color: var(--ring);
  ring-offset: 2px;
}

/* 主题切换动画 */
* {
  transition-property: background-color, border-color, color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
}

/* 主题相关工具类 */
.bg-background {
  background-color: var(--background);
}

.bg-card {
  background-color: var(--card-background);
}

.bg-muted {
  background-color: var(--muted);
}

.text-foreground {
  color: var(--foreground);
}

.text-card-foreground {
  color: var(--card-foreground);
}

.text-muted-foreground {
  color: var(--muted-foreground);
}

.border-border {
  border-color: var(--border);
}

.border-input {
  border-color: var(--input-border);
}

/* 高对比度文本确保可访问性 */
.text-high-contrast {
  color: var(--foreground);
  font-weight: 500;
}

.text-medium-contrast {
  color: var(--muted-foreground);
}

/* 主题感知的输入框样式 */
.input-themed {
  background-color: var(--input-background);
  border-color: var(--input-border);
  color: var(--foreground);
}

.input-themed:focus {
  border-color: var(--accent);
  ring-color: var(--ring);
}

/* 主题感知的按钮样式 */
.btn-primary {
  background-color: var(--accent);
  color: var(--accent-foreground);
  border-color: var(--accent);
}

.btn-primary:hover {
  opacity: 0.9;
}

.btn-secondary {
  background-color: var(--muted);
  color: var(--muted-foreground);
  border-color: var(--border);
}

.btn-secondary:hover {
  background-color: var(--border);
}
